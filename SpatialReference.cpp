#include "SpatialReference.hpp"
#include <osg/Math>
#include <osg/Notify>
#include <iostream>
#include <sstream>
#include <cmath>

using namespace EarthSample;

const double PI = 3.14159265358979323846;
const double WGS84_SEMI_MAJOR_AXIS = 6378137.0;
const double WGS84_SEMI_MINOR_AXIS = 6356752.31424518;
const double WGS84_FLATTENING = (WGS84_SEMI_MAJOR_AXIS - WGS84_SEMI_MINOR_AXIS) / WGS84_SEMI_MAJOR_AXIS;
const double WGS84_ECCENTRICITY_SQUARED = 2.0 * WGS84_FLATTENING - WGS84_FLATTENING * WGS84_FLATTENING;

// ======== Ellipsoid Implementation ========

Ellipsoid::Ellipsoid() : _semiMajorAxis(WGS84_SEMI_MAJOR_AXIS),
                         _semiMinorAxis(WGS84_SEMI_MINOR_AXIS),
                         _name("WGS84")
{
  _ellipsoidModel = new osg::EllipsoidModel(_semiMajorAxis, _semiMinorAxis);
}

Ellipsoid::Ellipsoid(double semiMajorAxis, double semiMinorAxis) : _semiMajorAxis(semiMajorAxis),
                                                                   _semiMinorAxis(semiMinorAxis),
                                                                   _name("Custom")
{
  _ellipsoidModel = new osg::EllipsoidModel(_semiMajorAxis, _semiMinorAxis);
}

osg::Matrix Ellipsoid::geocentricToLocalToWorld(const osg::Vec3d &xyz) const
{
  osg::Matrix m;
  _ellipsoidModel->computeLocalToWorldTransformFromXYZ(xyz.x(), xyz.y(), xyz.z(), m);
  return m;
}

osg::Vec3d Ellipsoid::geocentricToGeodetic(const osg::Vec3d &xyz) const
{
  osg::Vec3d lla;
  _ellipsoidModel->convertXYZToLatLongHeight(xyz.x(), xyz.y(), xyz.z(), lla.y(), lla.x(), lla.z());
  // 转换为度
  lla.x() = osg::RadiansToDegrees(lla.x());
  lla.y() = osg::RadiansToDegrees(lla.y());
  return lla;
}

osg::Vec3d Ellipsoid::geodeticToGeocentric(const osg::Vec3d &lla) const
{
  osg::Vec3d xyz;
  _ellipsoidModel->convertLatLongHeightToXYZ(
      osg::DegreesToRadians(lla.y()),
      osg::DegreesToRadians(lla.x()),
      lla.z(),
      xyz.x(), xyz.y(), xyz.z());
  return xyz;
}

osg::Vec3d Ellipsoid::geocentricToUpVector(const osg::Vec3d &xyz) const
{
  return _ellipsoidModel->computeLocalUpVector(xyz.x(), xyz.y(), xyz.z());
}

// ======== SpatialReference Implementation ========

SpatialReference::SpatialReference() : _type(GEOGRAPHIC),
                                       _projection(PROJ_LONGLAT),
                                       _valid(false),
                                       _centralMeridian(0.0),
                                       _falseEasting(0.0),
                                       _falseNorthing(0.0),
                                       _scaleFactor(1.0),
                                       _utmZone(0),
                                       _utmNorthern(true)
{
  init();
}

SpatialReference::~SpatialReference()
{
}

void SpatialReference::init()
{
  _ellipsoid = Ellipsoid(); // 默认WGS84
  _valid = true;
}

SpatialReference *SpatialReference::create(const std::string &wkt)
{
  SpatialReference *srs = new SpatialReference();
  srs->initFromWKT(wkt);
  return srs;
}

SpatialReference *SpatialReference::createGeographic()
{
  SpatialReference *srs = new SpatialReference();
  srs->_type = GEOGRAPHIC;
  srs->_projection = PROJ_LONGLAT;
  srs->_name = "WGS84 Geographic";
  srs->_units = "degrees";
  srs->_datum = "WGS84";
  srs->_wkt = "GEOGCS[\"WGS 84\",DATUM[\"WGS_1984\",SPHEROID[\"WGS 84\",6378137,298.257223563]],PRIMEM[\"Greenwich\",0],UNIT[\"degree\",0.0174532925199433]]";
  srs->_valid = true;
  return srs;
}

SpatialReference *SpatialReference::createWebMercator()
{
  SpatialReference *srs = new SpatialReference();
  srs->_type = PROJECTED;
  srs->_projection = PROJ_WEB_MERCATOR;
  srs->_name = "WGS 84 / Pseudo-Mercator";
  srs->_units = "metres";
  srs->_datum = "WGS84";
  srs->_wkt = "PROJCS[\"WGS 84 / Pseudo-Mercator\",GEOGCS[\"WGS 84\",DATUM[\"WGS_1984\",SPHEROID[\"WGS 84\",6378137,298.257223563]],PRIMEM[\"Greenwich\",0],UNIT[\"degree\",0.0174532925199433]],PROJECTION[\"Mercator_1SP\"],PARAMETER[\"central_meridian\",0],PARAMETER[\"scale_factor\",1],PARAMETER[\"false_easting\",0],PARAMETER[\"false_northing\",0],UNIT[\"metre\",1]]";
  srs->_valid = true;
  return srs;
}

SpatialReference *SpatialReference::createUTM(int zone, bool northern)
{
  SpatialReference *srs = new SpatialReference();
  srs->_type = PROJECTED;
  srs->_projection = PROJ_UTM;
  srs->_name = "WGS 84 / UTM zone " + std::to_string(zone) + (northern ? "N" : "S");
  srs->_units = "metres";
  srs->_datum = "WGS84";
  srs->_utmZone = zone;
  srs->_utmNorthern = northern;
  srs->_centralMeridian = -183.0 + zone * 6.0;
  srs->_scaleFactor = 0.9996;
  srs->_falseEasting = 500000.0;
  srs->_falseNorthing = northern ? 0.0 : 10000000.0;

  std::ostringstream oss;
  oss << "PROJCS[\"WGS 84 / UTM zone " << zone << (northern ? "N" : "S") << "\","
      << "GEOGCS[\"WGS 84\",DATUM[\"WGS_1984\",SPHEROID[\"WGS 84\",6378137,298.257223563]],"
      << "PRIMEM[\"Greenwich\",0],UNIT[\"degree\",0.0174532925199433]],"
      << "PROJECTION[\"Transverse_Mercator\"],"
      << "PARAMETER[\"latitude_of_origin\",0],"
      << "PARAMETER[\"central_meridian\"," << srs->_centralMeridian << "],"
      << "PARAMETER[\"scale_factor\",0.9996],"
      << "PARAMETER[\"false_easting\",500000],"
      << "PARAMETER[\"false_northing\"," << srs->_falseNorthing << "],"
      << "UNIT[\"metre\",1]]";
  srs->_wkt = oss.str();
  srs->_valid = true;
  return srs;
}

SpatialReference *SpatialReference::createGeocentric()
{
  SpatialReference *srs = new SpatialReference();
  srs->_type = GEOCENTRIC;
  srs->_projection = PROJ_GEOCENTRIC;
  srs->_name = "WGS 84 Geocentric";
  srs->_units = "metres";
  srs->_datum = "WGS84";
  srs->_wkt = "GEOCCS[\"WGS 84\",DATUM[\"WGS_1984\",SPHEROID[\"WGS 84\",6378137,298.257223563]],PRIMEM[\"Greenwich\",0],UNIT[\"metre\",1]]";
  srs->_valid = true;
  return srs;
}

void SpatialReference::initFromWKT(const std::string &wkt)
{
  _wkt = wkt;

  // 简单的WKT解析
  if (wkt.find("GEOGCS") != std::string::npos)
  {
    _type = GEOGRAPHIC;
    _projection = PROJ_LONGLAT;
    _units = "degrees";
  }
  else if (wkt.find("PROJCS") != std::string::npos)
  {
    _type = PROJECTED;
    if (wkt.find("Mercator") != std::string::npos)
    {
      if (wkt.find("Pseudo-Mercator") != std::string::npos)
      {
        _projection = PROJ_WEB_MERCATOR;
      }
      else
      {
        _projection = PROJ_MERCATOR;
      }
    }
    else if (wkt.find("Transverse_Mercator") != std::string::npos)
    {
      _projection = PROJ_TRANSVERSE_MERCATOR;
    }
    _units = "metres";
  }
  else if (wkt.find("GEOCCS") != std::string::npos)
  {
    _type = GEOCENTRIC;
    _projection = PROJ_GEOCENTRIC;
    _units = "metres";
  }

  _valid = true;
}

bool SpatialReference::transform(const osg::Vec3d &input, const SpatialReference *outputSRS, osg::Vec3d &output) const
{
  if (!_valid || !outputSRS || !outputSRS->valid())
  {
    return false;
  }

  if (this == outputSRS)
  {
    output = input;
    return true;
  }

  // 通过地理坐标系统进行转换
  osg::Vec3d geographic;

  // 输入转换为地理坐标
  if (isGeographic())
  {
    geographic = input;
  }
  else if (isProjected() && _projection == PROJ_WEB_MERCATOR)
  {
    if (!transformWebMercatorToGeographic(input, geographic))
    {
      return false;
    }
  }
  else if (isGeocentric())
  {
    if (!transformGeocentricToGeographic(input, geographic))
    {
      return false;
    }
  }
  else
  {
    return false; // 不支持的输入坐标系统
  }

  // 地理坐标转换为输出坐标系统
  if (outputSRS->isGeographic())
  {
    output = geographic;
  }
  else if (outputSRS->isProjected() && outputSRS->_projection == PROJ_WEB_MERCATOR)
  {
    if (!outputSRS->transformGeographicToWebMercator(geographic, output))
    {
      return false;
    }
  }
  else if (outputSRS->isGeocentric())
  {
    if (!outputSRS->transformGeographicToGeocentric(geographic, output))
    {
      return false;
    }
  }
  else
  {
    return false; // 不支持的输出坐标系统
  }

  return true;
}

bool SpatialReference::transformPoints(const std::vector<osg::Vec3d> &input, const SpatialReference *outputSRS, std::vector<osg::Vec3d> &output) const
{
  output.resize(input.size());
  for (size_t i = 0; i < input.size(); ++i)
  {
    if (!transform(input[i], outputSRS, output[i]))
    {
      return false;
    }
  }
  return true;
}

bool SpatialReference::transformGeographicToGeocentric(const osg::Vec3d &input, osg::Vec3d &output) const
{
  output = _ellipsoid.geodeticToGeocentric(input);
  return true;
}

bool SpatialReference::transformGeocentricToGeographic(const osg::Vec3d &input, osg::Vec3d &output) const
{
  output = _ellipsoid.geocentricToGeodetic(input);
  return true;
}

bool SpatialReference::transformGeographicToWebMercator(const osg::Vec3d &input, osg::Vec3d &output) const
{
  // Web墨卡托投影公式
  double lon = input.x() * PI / 180.0;
  double lat = input.y() * PI / 180.0;

  // 限制纬度范围
  if (std::abs(input.y()) > 85.0511)
  {
    return false;
  }

  output.x() = WGS84_SEMI_MAJOR_AXIS * lon;
  output.y() = WGS84_SEMI_MAJOR_AXIS * std::log(std::tan(PI * 0.25 + lat * 0.5));
  output.z() = input.z();

  return true;
}

bool SpatialReference::transformWebMercatorToGeographic(const osg::Vec3d &input, osg::Vec3d &output) const
{
  // Web墨卡托反投影公式
  double x = input.x() / WGS84_SEMI_MAJOR_AXIS;
  double y = input.y() / WGS84_SEMI_MAJOR_AXIS;

  output.x() = x * 180.0 / PI;
  output.y() = (2.0 * std::atan(std::exp(y)) - PI * 0.5) * 180.0 / PI;
  output.z() = input.z();

  return true;
}

const SpatialReference *SpatialReference::getGeographicSRS() const
{
  if (!_geographicSRS.valid())
  {
    _geographicSRS = createGeographic();
  }
  return _geographicSRS.get();
}

const SpatialReference *SpatialReference::getGeocentricSRS() const
{
  if (!_geocentricSRS.valid())
  {
    _geocentricSRS = createGeocentric();
  }
  return _geocentricSRS.get();
}

bool SpatialReference::createLocalToWorld(const osg::Vec3d &xyz, osg::Matrix &out_local2world) const
{
  if (!_valid)
    return false;

  if (isProjected())
  {
    // 投影坐标系统：转换为世界坐标（地心坐标）
    osg::Vec3d world;
    if (!transform(xyz, getGeocentricSRS(), world))
    {
      return false;
    }
    out_local2world = osg::Matrix::translate(world);
  }
  else if (isGeocentric())
  {
    // 地心坐标系统：创建本地切平面坐标系统
    out_local2world = _ellipsoid.geocentricToLocalToWorld(xyz);
  }
  else
  {
    // 地理坐标系统：转换为地心坐标
    osg::Vec3d ecef;
    if (!transform(xyz, getGeocentricSRS(), ecef))
    {
      return false;
    }
    out_local2world = _ellipsoid.geocentricToLocalToWorld(ecef);
  }

  return true;
}

bool SpatialReference::createWorldToLocal(const osg::Vec3d &xyz, osg::Matrix &out_world2local) const
{
  osg::Matrix local2world;
  if (!createLocalToWorld(xyz, local2world))
  {
    return false;
  }
  out_world2local.invert(local2world);
  return true;
}

bool SpatialReference::populateCoordinateSystemNode(osg::CoordinateSystemNode *csn) const
{
  if (!csn || !_valid)
    return false;

  if (!_wkt.empty())
  {
    csn->setFormat("WKT");
    csn->setCoordinateSystem(_wkt);
  }

  csn->setEllipsoidModel(new osg::EllipsoidModel(
      _ellipsoid.getSemiMajorAxis(),
      _ellipsoid.getSemiMinorAxis()));

  return true;
}

// ======== GeoExtent Implementation ========

GeoExtent::GeoExtent() : _xmin(0.0), _ymin(0.0), _xmax(0.0), _ymax(0.0)
{
}

GeoExtent::GeoExtent(const SpatialReference *srs, double xmin, double ymin, double xmax, double ymax) : _srs(srs),
                                                                                                        _xmin(xmin), _ymin(ymin), _xmax(xmax), _ymax(ymax)
{
}

bool GeoExtent::intersects(const GeoExtent &other) const
{
  if (!valid() || !other.valid())
    return false;

  return !(_xmax < other._xmin || _xmin > other._xmax ||
           _ymax < other._ymin || _ymin > other._ymax);
}

bool GeoExtent::contains(const osg::Vec2d &point) const
{
  return contains(point.x(), point.y());
}

bool GeoExtent::contains(double x, double y) const
{
  if (!valid())
    return false;

  return (x >= _xmin && x <= _xmax && y >= _ymin && y <= _ymax);
}

GeoExtent GeoExtent::transform(const SpatialReference *targetSRS) const
{
  if (!valid() || !targetSRS)
    return GeoExtent();

  // 转换四个角点
  std::vector<osg::Vec3d> corners = {
      osg::Vec3d(_xmin, _ymin, 0.0),
      osg::Vec3d(_xmax, _ymin, 0.0),
      osg::Vec3d(_xmax, _ymax, 0.0),
      osg::Vec3d(_xmin, _ymax, 0.0)};

  std::vector<osg::Vec3d> transformedCorners;
  if (!_srs->transformPoints(corners, targetSRS, transformedCorners))
  {
    return GeoExtent();
  }

  // 计算转换后的边界
  double xmin = transformedCorners[0].x();
  double ymin = transformedCorners[0].y();
  double xmax = transformedCorners[0].x();
  double ymax = transformedCorners[0].y();

  for (const auto &corner : transformedCorners)
  {
    xmin = std::min(xmin, corner.x());
    ymin = std::min(ymin, corner.y());
    xmax = std::max(xmax, corner.x());
    ymax = std::max(ymax, corner.y());
  }

  return GeoExtent(targetSRS, xmin, ymin, xmax, ymax);
}

// ======== GeoPoint Implementation ========

GeoPoint::GeoPoint() : _x(0.0), _y(0.0), _z(0.0)
{
}

GeoPoint::GeoPoint(const SpatialReference *srs, double x, double y, double z) : _srs(srs), _x(x), _y(y), _z(z), _xyz(x, y, z)
{
}

GeoPoint::GeoPoint(const SpatialReference *srs, const osg::Vec3d &xyz) : _srs(srs), _x(xyz.x()), _y(xyz.y()), _z(xyz.z()), _xyz(xyz)
{
}

void GeoPoint::set(const SpatialReference *srs, double x, double y, double z)
{
  _srs = srs;
  _x = x;
  _y = y;
  _z = z;
  _xyz.set(x, y, z);
}

void GeoPoint::set(const SpatialReference *srs, const osg::Vec3d &xyz)
{
  _srs = srs;
  _x = xyz.x();
  _y = xyz.y();
  _z = xyz.z();
  _xyz = xyz;
}

GeoPoint GeoPoint::transform(const SpatialReference *targetSRS) const
{
  GeoPoint result;
  if (transform(targetSRS, result))
  {
    return result;
  }
  return GeoPoint(); // 无效点
}

bool GeoPoint::transform(const SpatialReference *targetSRS, GeoPoint &output) const
{
  if (!valid() || !targetSRS)
    return false;

  osg::Vec3d transformed;
  if (!_srs->transform(_xyz, targetSRS, transformed))
  {
    return false;
  }

  output.set(targetSRS, transformed);
  return true;
}

osg::Vec3d GeoPoint::toWorld() const
{
  if (!valid())
    return osg::Vec3d();

  if (_srs->isGeocentric())
  {
    return _xyz;
  }

  // 转换为地心坐标
  osg::Vec3d world;
  if (_srs->transform(_xyz, _srs->getGeocentricSRS(), world))
  {
    return world;
  }

  return osg::Vec3d();
}