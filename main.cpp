// Enhanced OSG Earth Sample - 增强版数字地球示例
// 集成完整的坐标系统支持和瓦片贴图功能
// 支持桌面版和WebAssembly版本

// WebAssembly/Emscripten兼容性修复
#ifdef EMSCRIPTEN
#define _LIBCPP_HAS_NO_PRAGMA_SYSTEM_HEADER
#define _LIBCPP_DISABLE_AVAILABILITY
#endif

// Windows/MSVC兼容性修复
#ifdef _WIN32
#define _USE_MATH_DEFINES
#define NOMINMAX // 防止Windows.h定义min/max宏
#endif

#include <iostream>
#include <memory>
#include <vector>
#include <string>
#include <sstream>
#include <map>
#include <algorithm>
#include <cmath>
#include <functional>
#include <thread>
#include <chrono>

// 项目头文件
#include "SpatialReference.hpp"
#include "TileSystem.hpp"

// OSG核心头文件
#include <osg/ref_ptr>
#include <osg/Group>
#include <osg/MatrixTransform>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/Texture2D>
#include <osg/Material>
#include <osg/LightModel>
#include <osg/CullFace>
#include <osg/Depth>
#include <osg/BlendFunc>
#include <osg/Image>
#include <osg/StateSet>
#include <osg/Program>
#include <osg/Shader>
#include <osg/Uniform>
#include <osg/TexEnv>
#include <osg/ShapeDrawable>
#include <osg/Shape>
#include <osg/CoordinateSystemNode>
#include <osg/ComputeBoundsVisitor>

// OSG Viewer
#include <osgViewer/Viewer>
#include <osgViewer/ViewerEventHandlers>
#include <osgGA/TrackballManipulator>
#include <osgGA/StateSetManipulator>
#include <osgUtil/LineSegmentIntersector>
#include <osg/Math>

#ifdef EMSCRIPTEN
#include <osgViewer/GraphicsWindow>
// WebGL GraphicsWindow 头文件在当前OSG版本中不可用
#endif

// 平台相关头文件
#ifdef EMSCRIPTEN
#include <SDL2/SDL.h>
#include <emscripten.h>
#include <emscripten/html5.h>
#include <emscripten/fetch.h>
#else
#include <SDL.h>
#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif
#endif

using namespace EarthSample;

// 全局变量
struct AppContext
{
    osg::ref_ptr<osgViewer::Viewer> viewer;
    osg::ref_ptr<osg::Group> rootNode;
    osg::ref_ptr<osg::MatrixTransform> earthTransform;
    osg::ref_ptr<osg::Geode> earthGeode;

    std::shared_ptr<TileSystemManager> tileManager;
    std::shared_ptr<TileDownloader> tileDownloader;
    osg::ref_ptr<const SpatialReference> geographicSRS;
    osg::ref_ptr<const SpatialReference> webMercatorSRS;

    SDL_Window *window;
    SDL_GLContext context;

    // 交互状态
    bool isDragging;
    int lastMouseX, lastMouseY;
    float rotationX, rotationY;
    float distance;

    // 坐标显示
    bool showCoordinates;
    GeoPoint currentMousePos;

    // 瓦片服务配置
    std::string currentTileService;
    std::vector<std::string> availableTileServices;

    // 程序退出标志
    bool shouldExit;

    AppContext() : isDragging(false),
                   lastMouseX(0), lastMouseY(0),
                   rotationX(0.0f), rotationY(0.0f),
                   distance(3.0f),
                   showCoordinates(true),
                   currentTileService("google_satellite"),
                   shouldExit(false)
    {
    }
};

AppContext *g_appContext = nullptr;

// ======== 函数前向声明 ========

void updateEarthTransform();
void updateMouseCoordinates(int mouseX, int mouseY);
void loadTilesForView();
void cleanup(); // 添加清理函数声明

// ======== 坐标系统功能 ========

/**
 * 创建坐标系统节点
 */
osg::ref_ptr<osg::CoordinateSystemNode> createCoordinateSystemNode()
{
    osg::ref_ptr<osg::CoordinateSystemNode> csn = new osg::CoordinateSystemNode();

    // 使用WGS84地理坐标系统
    auto geographicSRS = SpatialReference::createGeographic();
    geographicSRS->populateCoordinateSystemNode(csn.get());

    return csn;
}

/**
 * 坐标转换示例
 */
void demonstrateCoordinateTransform()
{
    std::cout << "=== 坐标系统转换示例 ===" << std::endl;

    // 创建不同的坐标系统
    osg::ref_ptr<const SpatialReference> geographicSRS = SpatialReference::createGeographic();
    osg::ref_ptr<const SpatialReference> webMercatorSRS = SpatialReference::createWebMercator();
    osg::ref_ptr<const SpatialReference> geocentricSRS = SpatialReference::createGeocentric();

    // 示例点：北京的坐标
    GeoPoint beijingLL(geographicSRS, 116.4074, 39.9042, 0.0);
    std::cout << "北京地理坐标: " << beijingLL.x() << ", " << beijingLL.y() << std::endl;

    // 转换为Web墨卡托坐标
    GeoPoint beijingMercator = beijingLL.transform(webMercatorSRS);
    if (beijingMercator.valid())
    {
        std::cout << "北京Web墨卡托坐标: " << beijingMercator.x() << ", " << beijingMercator.y() << std::endl;
    }

    // 转换为地心坐标
    GeoPoint beijingGeocentric = beijingLL.transform(geocentricSRS);
    if (beijingGeocentric.valid())
    {
        std::cout << "北京地心坐标: " << beijingGeocentric.x() << ", " << beijingGeocentric.y() << ", " << beijingGeocentric.z() << std::endl;
    }

    // 创建地理范围
    GeoExtent chinaExtent(geographicSRS, 73.0, 18.0, 135.0, 54.0);
    std::cout << "中国地理范围: " << chinaExtent.width() << " x " << chinaExtent.height() << " 度" << std::endl;

    // 转换范围到Web墨卡托
    GeoExtent chinaMercatorExtent = chinaExtent.transform(webMercatorSRS);
    if (chinaMercatorExtent.valid())
    {
        std::cout << "中国Web墨卡托范围: " << chinaMercatorExtent.width() << " x " << chinaMercatorExtent.height() << " 米" << std::endl;
    }
}

// ======== 瓦片系统功能 ========

/**
 * 瓦片加载回调类
 */
class EarthTileLoadCallback : public TileLoadCallback
{
public:
    EarthTileLoadCallback(AppContext *context) : _context(context) {}

    virtual void onTileLoaded(const TileData &tile) override
    {
        std::cout << "瓦片加载成功: " << tile.getKey().toString() << std::endl;

        // 创建纹理
        osg::ref_ptr<osg::Texture2D> texture = tile.createOSGTexture();
        if (texture.valid())
        {
            // 应用纹理到地球几何体
            updateEarthTexture(texture);
        }
    }

    virtual void onTileLoadError(const TileKey &key, const std::string &error) override
    {
        std::cout << "瓦片加载失败: " << key.toString() << ", 错误: " << error << std::endl;
    }

    virtual void onTileLoadProgress(const TileKey &key, double progress) override
    {
        std::cout << "瓦片加载进度: " << key.toString() << " - " << (progress * 100.0) << "%" << std::endl;
    }

private:
    AppContext *_context;

    void updateEarthTexture(osg::Texture2D *texture)
    {
        if (!_context || !_context->earthGeode.valid())
            return;

        // 获取地球几何体的状态集
        osg::StateSet *stateSet = _context->earthGeode->getOrCreateStateSet();

        // 应用纹理
        // 在现代OpenGL/WebGL中，纹理环境(TexEnv)已弃用。
        // 纹理的应用方式由片段着色器决定。
        // 默认情况下，纹理颜色会与材质的漫反射颜色相乘。
        stateSet->setTextureAttributeAndModes(0, texture, osg::StateAttribute::ON);
    }
};

/**
 * 初始化瓦片系统
 */
void initializeTileSystem()
{
    if (!g_appContext)
        return;

    // 创建瓦片系统管理器
    g_appContext->tileManager = std::make_shared<TileSystemManager>();

    // 获取可用的瓦片服务
    g_appContext->availableTileServices = g_appContext->tileManager->getTileServiceNames();

    // 创建瓦片下载器
    g_appContext->tileDownloader = g_appContext->tileManager->createTileDownloader(g_appContext->currentTileService);

    if (g_appContext->tileDownloader)
    {
        // 设置回调
        auto callback = std::make_shared<EarthTileLoadCallback>(g_appContext);
        g_appContext->tileDownloader->setLoadCallback(callback);

        // 设置下载参数
        g_appContext->tileDownloader->setMaxConcurrentDownloads(6);
        g_appContext->tileDownloader->setTimeout(30.0);

        // 设置代理（如果需要）
        g_appContext->tileDownloader->setProxy("127.0.0.1", 10809);
        std::cout << "已设置代理: 127.0.0.1:10809" << std::endl;

        std::cout << "瓦片系统初始化成功" << std::endl;
        std::cout << "当前瓦片服务: " << g_appContext->currentTileService << std::endl;
        std::cout << "可用瓦片服务: ";
        for (const auto &service : g_appContext->availableTileServices)
        {
            std::cout << service << " ";
        }
        std::cout << std::endl;
    }
}

/**
 * 加载基础瓦片
 */
void loadBaseTiles()
{
    if (!g_appContext || !g_appContext->tileDownloader)
        return;

    std::cout << "开始加载基础瓦片..." << std::endl;

    // 加载Level 0和Level 1的瓦片
    std::vector<TileKey> tilesToLoad;

    // Level 0 - 全球概览
    for (int y = 0; y < 1; ++y)
    {
        for (int x = 0; x < 1; ++x)
        {
            tilesToLoad.push_back(TileKey(0, x, y));
        }
    }

    // Level 1 - 更详细的全球瓦片
    for (int y = 0; y < 2; ++y)
    {
        for (int x = 0; x < 2; ++x)
        {
            tilesToLoad.push_back(TileKey(1, x, y));
        }
    }

    // 批量下载
    g_appContext->tileDownloader->downloadTiles(tilesToLoad);

    std::cout << "已请求加载 " << tilesToLoad.size() << " 个瓦片" << std::endl;
}

/**
 * 动态加载瓦片
 */
void loadTilesForView()
{
    if (!g_appContext || !g_appContext->tileDownloader || !g_appContext->viewer.valid())
        return;

    // 根据当前视角计算需要加载的瓦片
    osg::Camera *camera = g_appContext->viewer->getCamera();
    osg::Matrixd viewMatrix = camera->getViewMatrix();
    osg::Matrixd projMatrix = camera->getProjectionMatrix();

    // 简化实现：根据距离选择LOD级别
    int lodLevel = 2;
    if (g_appContext->distance < 2.0f)
    {
        lodLevel = 3;
    }
    else if (g_appContext->distance < 1.5f)
    {
        lodLevel = 4;
    }

    // 加载中心区域的瓦片
    std::vector<TileKey> tilesToLoad;
    int centerX = (1 << lodLevel) / 2;
    int centerY = (1 << lodLevel) / 2;

    for (int dy = -1; dy <= 1; ++dy)
    {
        for (int dx = -1; dx <= 1; ++dx)
        {
            int x = centerX + dx;
            int y = centerY + dy;
            if (x >= 0 && y >= 0 && x < (1 << lodLevel) && y < (1 << lodLevel))
            {
                tilesToLoad.push_back(TileKey(lodLevel, x, y));
            }
        }
    }

    // 下载瓦片
    for (const auto &key : tilesToLoad)
    {
        if (!g_appContext->tileDownloader->isDownloading(key))
        {
            g_appContext->tileDownloader->downloadTile(key);
        }
    }
}

// ======== 地球几何体创建 ========

/**
 * 创建高质量球体几何体
 */
osg::ref_ptr<osg::Geometry> createEarthGeometry()
{
    const int rings = 64;
    const int segments = 128;
    const float radius = 1.0f;

    osg::ref_ptr<osg::Geometry> geometry = new osg::Geometry();
    osg::ref_ptr<osg::Vec3Array> vertices = new osg::Vec3Array();
    osg::ref_ptr<osg::Vec3Array> normals = new osg::Vec3Array();
    osg::ref_ptr<osg::Vec2Array> texCoords = new osg::Vec2Array();

    // 生成顶点
    for (int ring = 0; ring <= rings; ++ring)
    {
        float phi = M_PI * ring / rings;
        float y = radius * cos(phi);
        float ringRadius = radius * sin(phi);

        for (int segment = 0; segment <= segments; ++segment)
        {
            float theta = 2.0f * M_PI * segment / segments;
            float x = ringRadius * cos(theta);
            float z = ringRadius * sin(theta);

            osg::Vec3f vertex(x, y, z);
            osg::Vec3f normal = vertex;
            normal.normalize();

            vertices->push_back(vertex);
            normals->push_back(normal);

            // 纹理坐标 - 修复球面纹理坐标映射
            float u = (float)segment / segments;
            float v = 1.0f - (float)ring / rings; // 翻转V坐标以匹配地球纹理方向

            // 确保UV坐标在正确范围内
            u = fmodf(u, 1.0f);
            v = std::max(0.0f, std::min(1.0f, v));

            texCoords->push_back(osg::Vec2f(u, v));
        }
    }

    // 创建三角形索引
    osg::ref_ptr<osg::DrawElementsUInt> indices = new osg::DrawElementsUInt(osg::PrimitiveSet::TRIANGLES);

    for (int ring = 0; ring < rings; ++ring)
    {
        for (int segment = 0; segment < segments; ++segment)
        {
            int current = ring * (segments + 1) + segment;
            int next = current + segments + 1;

            // 第一个三角形
            indices->push_back(current);
            indices->push_back(next);
            indices->push_back(current + 1);

            // 第二个三角形
            indices->push_back(current + 1);
            indices->push_back(next);
            indices->push_back(next + 1);
        }
    }

    geometry->setVertexArray(vertices);
    geometry->setNormalArray(normals);
    geometry->setNormalBinding(osg::Geometry::BIND_PER_VERTEX);
    geometry->setTexCoordArray(0, texCoords);
    geometry->addPrimitiveSet(indices);

    return geometry;
}

/**
 * 创建程序化地球纹理
 */
osg::ref_ptr<osg::Texture2D> createProceduralEarthTexture()
{
    const int width = 1024;
    const int height = 512;

    osg::ref_ptr<osg::Image> image = new osg::Image();
    image->allocateImage(width, height, 1, GL_RGB, GL_UNSIGNED_BYTE);

    unsigned char *data = image->data();

    for (int y = 0; y < height; ++y)
    {
        for (int x = 0; x < width; ++x)
        {
            // 将像素坐标转换为地理坐标
            float lon = (float)x / width * 360.0f - 180.0f;
            float lat = 90.0f - (float)y / height * 180.0f;

            // 基于地理特征的颜色生成
            osg::Vec3f color(0.0f, 0.4f, 0.8f); // 默认海洋蓝

            // 简单的陆地模拟
            float landFactor = 0.0f;

            // 模拟大陆分布
            if (abs(lat) < 70.0f)
            {
                landFactor += 0.3f;
            }

            // 模拟主要大陆
            if ((lon > -140.0f && lon < -50.0f && lat > 20.0f && lat < 70.0f) || // 北美
                (lon > -20.0f && lon < 50.0f && lat > 35.0f && lat < 70.0f) ||   // 欧洲
                (lon > 70.0f && lon < 140.0f && lat > 10.0f && lat < 55.0f) ||   // 亚洲
                (lon > -80.0f && lon < -30.0f && lat > -60.0f && lat < 15.0f) || // 南美
                (lon > 10.0f && lon < 55.0f && lat > -35.0f && lat < 35.0f) ||   // 非洲
                (lon > 110.0f && lon < 180.0f && lat > -50.0f && lat < -10.0f))  // 澳洲
            {
                landFactor += 0.7f;
            }

            // 极地冰盖
            if (abs(lat) > 80.0f)
            {
                color = osg::Vec3f(0.9f, 0.9f, 1.0f); // 冰雪白
            }
            // 陆地颜色
            else if (landFactor > 0.5f)
            {
                float elevation = sin(lon * 0.1f) * sin(lat * 0.1f) * 0.5f + 0.5f;
                if (elevation > 0.7f)
                {
                    color = osg::Vec3f(0.6f, 0.4f, 0.2f); // 山脉褐色
                }
                else if (abs(lat) < 30.0f)
                {
                    color = osg::Vec3f(0.8f, 0.7f, 0.3f); // 沙漠黄
                }
                else
                {
                    color = osg::Vec3f(0.2f, 0.6f, 0.1f); // 植被绿
                }
            }

            // 添加一些随机变化
            float noise = (sin(lon * 0.5f) * cos(lat * 0.3f) + 1.0f) * 0.5f;
            color *= (0.8f + noise * 0.2f);

            // 写入像素数据
            int index = (y * width + x) * 3;
            data[index] = (unsigned char)(color.x() * 255);
            data[index + 1] = (unsigned char)(color.y() * 255);
            data[index + 2] = (unsigned char)(color.z() * 255);
        }
    }

    // 创建纹理
    osg::ref_ptr<osg::Texture2D> texture = new osg::Texture2D();
    texture->setImage(image);

    // 设置纹理包装模式
    texture->setWrap(osg::Texture::WRAP_S, osg::Texture::REPEAT);
    texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);

    // 设置过滤模式
    texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR);
    texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);

#ifdef EMSCRIPTEN
    // WebAssembly版本额外设置：确保WebGL兼容性
    texture->setInternalFormat(GL_RGB);
    texture->setSourceFormat(GL_RGB);
    texture->setSourceType(GL_UNSIGNED_BYTE);

    // 强制纹理上传到GPU
    texture->setUnRefImageDataAfterApply(false);
    texture->setResizeNonPowerOfTwoHint(false);

    std::cout << "创建程序化地球纹理: " << width << "x" << height << " (WebGL兼容)" << std::endl;
#else
    // 桌面版本：允许自动格式选择
    texture->setInternalFormat(GL_RGB8);
    std::cout << "创建程序化地球纹理: " << width << "x" << height << " (桌面版)" << std::endl;
#endif

    return texture;
}

/**
 * 创建地球节点
 */
osg::ref_ptr<osg::Group> createEarthNode()
{
    osg::ref_ptr<osg::Group> earthGroup = new osg::Group();

    // 创建坐标系统节点
    osg::ref_ptr<osg::CoordinateSystemNode> csn = createCoordinateSystemNode();
    earthGroup->addChild(csn);

    // 创建地球几何体
    g_appContext->earthGeode = new osg::Geode();
    osg::ref_ptr<osg::Geometry> earthGeometry = createEarthGeometry();
    g_appContext->earthGeode->addDrawable(earthGeometry);

    // 创建程序化纹理
    osg::ref_ptr<osg::Texture2D> proceduralTexture = createProceduralEarthTexture();

    // 设置状态
    osg::StateSet *stateSet = g_appContext->earthGeode->getOrCreateStateSet();
    stateSet->setTextureAttributeAndModes(0, proceduralTexture, osg::StateAttribute::ON);

#ifdef EMSCRIPTEN
    // WebAssembly版本：修复纹理全黑问题
    // 1. 设置纹理环境模式为REPLACE，避免与材质颜色相乘导致全黑
    osg::TexEnv *texEnv = new osg::TexEnv();
    texEnv->setMode(osg::TexEnv::REPLACE);
    stateSet->setTextureAttributeAndModes(0, texEnv, osg::StateAttribute::ON);

    // 2. 禁用光照，使用纹理本身的颜色
    stateSet->setMode(GL_LIGHTING, osg::StateAttribute::OFF);

    // 3. 确保深度测试正常工作
    stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);

    // 4. 设置简单的材质，避免颜色干扰
    osg::Material *material = new osg::Material();
    material->setDiffuse(osg::Material::FRONT_AND_BACK, osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f));
    material->setAmbient(osg::Material::FRONT_AND_BACK, osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f));
    material->setSpecular(osg::Material::FRONT_AND_BACK, osg::Vec4(0.0f, 0.0f, 0.0f, 1.0f));
    material->setEmission(osg::Material::FRONT_AND_BACK, osg::Vec4(0.0f, 0.0f, 0.0f, 1.0f));
    stateSet->setAttributeAndModes(material, osg::StateAttribute::ON);

    std::cout << "WebAssembly纹理渲染设置：禁用光照，使用REPLACE模式" << std::endl;
#else
    // 桌面版本：使用标准光照模型
    osg::Material *material = new osg::Material();
    material->setDiffuse(osg::Material::FRONT_AND_BACK, osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f));
    material->setAmbient(osg::Material::FRONT_AND_BACK, osg::Vec4(0.3f, 0.3f, 0.3f, 1.0f));
    stateSet->setAttributeAndModes(material, osg::StateAttribute::ON);
    stateSet->setMode(GL_LIGHTING, osg::StateAttribute::ON);
    stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);
#endif

    // 添加到变换节点
    g_appContext->earthTransform = new osg::MatrixTransform();
    g_appContext->earthTransform->addChild(g_appContext->earthGeode);

    earthGroup->addChild(g_appContext->earthTransform);

    return earthGroup;
}

// ======== 交互处理 ========

/**
 * 处理鼠标事件
 */
void handleMouseEvent(const SDL_Event &event)
{
    if (!g_appContext)
        return;

    switch (event.type)
    {
    case SDL_MOUSEBUTTONDOWN:
        if (event.button.button == SDL_BUTTON_LEFT)
        {
            g_appContext->isDragging = true;
            g_appContext->lastMouseX = event.button.x;
            g_appContext->lastMouseY = event.button.y;
        }
        break;

    case SDL_MOUSEBUTTONUP:
        if (event.button.button == SDL_BUTTON_LEFT)
        {
            g_appContext->isDragging = false;
        }
        break;

    case SDL_MOUSEMOTION:
        if (g_appContext->isDragging)
        {
            int deltaX = event.motion.x - g_appContext->lastMouseX;
            int deltaY = event.motion.y - g_appContext->lastMouseY;

            g_appContext->rotationY += deltaX * 0.5f;
            g_appContext->rotationX += deltaY * 0.5f;

            // 限制X轴旋转
            g_appContext->rotationX = std::max(-89.0f, std::min(89.0f, g_appContext->rotationX));

            g_appContext->lastMouseX = event.motion.x;
            g_appContext->lastMouseY = event.motion.y;

            // 更新变换矩阵
            updateEarthTransform();
        }

        // 更新鼠标位置对应的地理坐标
        if (g_appContext->showCoordinates)
        {
            updateMouseCoordinates(event.motion.x, event.motion.y);
        }
        break;

    case SDL_MOUSEWHEEL:
        if (event.wheel.y > 0)
        {
            g_appContext->distance *= 0.9f;
        }
        else if (event.wheel.y < 0)
        {
            g_appContext->distance *= 1.1f;
        }

        // 限制距离范围
        g_appContext->distance = std::max(1.2f, std::min(10.0f, g_appContext->distance));

        updateEarthTransform();

        // 根据距离动态加载瓦片
        loadTilesForView();
        break;
    }
}

/**
 * 更新地球变换矩阵
 */
void updateEarthTransform()
{
    if (!g_appContext || !g_appContext->earthTransform.valid())
        return;

    // 创建变换矩阵
    osg::Matrix transform;

    // 旋转
    transform *= osg::Matrix::rotate(osg::DegreesToRadians(g_appContext->rotationX), osg::Vec3(1, 0, 0));
    transform *= osg::Matrix::rotate(osg::DegreesToRadians(g_appContext->rotationY), osg::Vec3(0, 1, 0));

    g_appContext->earthTransform->setMatrix(transform);

    // 更新相机位置
    if (g_appContext->viewer.valid())
    {
        osg::Vec3 eye(0, 0, g_appContext->distance);
        osg::Vec3 center(0, 0, 0);
        osg::Vec3 up(0, 1, 0);

        g_appContext->viewer->getCamera()->setViewMatrixAsLookAt(eye, center, up);
    }
}

/**
 * 更新鼠标坐标显示
 */
void updateMouseCoordinates(int mouseX, int mouseY)
{
    if (!g_appContext || !g_appContext->viewer.valid())
        return;

    // 这里可以实现鼠标拾取功能，将屏幕坐标转换为地理坐标
    // 简化实现，仅作为示例

    // 获取视口大小
    osg::Camera *camera = g_appContext->viewer->getCamera();
    const osg::Viewport *viewport = camera->getViewport();

    if (viewport)
    {
        float normX = (float)mouseX / viewport->width();
        float normY = 1.0f - (float)mouseY / viewport->height();

        // 简单的球面坐标转换
        float lon = (normX - 0.5f) * 360.0f;
        float lat = (normY - 0.5f) * 180.0f;

        // 更新当前鼠标位置
        g_appContext->currentMousePos.set(g_appContext->geographicSRS, lon, lat, 0.0);

        // 输出坐标信息（在实际应用中可以显示在UI上）
        static int frameCount = 0;
        if (frameCount % 60 == 0)
        { // 每60帧输出一次
            std::cout << "鼠标位置 - 经度: " << lon << "°, 纬度: " << lat << "°" << std::endl;
        }
        frameCount++;
    }
}

/**
 * 处理键盘事件
 */
void handleKeyboardEvent(const SDL_Event &event)
{
    if (!g_appContext)
        return;

    if (event.type == SDL_KEYDOWN)
    {
        switch (event.key.keysym.sym)
        {
        case SDLK_r:
            // 重置视角
            g_appContext->rotationX = 0.0f;
            g_appContext->rotationY = 0.0f;
            g_appContext->distance = 3.0f;
            updateEarthTransform();
            std::cout << "视角已重置" << std::endl;
            break;

        case SDLK_c:
            // 切换坐标显示
            g_appContext->showCoordinates = !g_appContext->showCoordinates;
            std::cout << "坐标显示: " << (g_appContext->showCoordinates ? "开启" : "关闭") << std::endl;
            break;

        case SDLK_t:
            // 切换瓦片服务
            if (!g_appContext->availableTileServices.empty())
            {
                auto it = std::find(g_appContext->availableTileServices.begin(),
                                    g_appContext->availableTileServices.end(),
                                    g_appContext->currentTileService);
                if (it != g_appContext->availableTileServices.end())
                {
                    ++it;
                    if (it == g_appContext->availableTileServices.end())
                    {
                        it = g_appContext->availableTileServices.begin();
                    }
                    g_appContext->currentTileService = *it;

                    // 重新创建下载器
                    g_appContext->tileDownloader = g_appContext->tileManager->createTileDownloader(g_appContext->currentTileService);
                    if (g_appContext->tileDownloader)
                    {
                        auto callback = std::make_shared<EarthTileLoadCallback>(g_appContext);
                        g_appContext->tileDownloader->setLoadCallback(callback);
                        loadBaseTiles();
                    }

                    std::cout << "切换瓦片服务: " << g_appContext->currentTileService << std::endl;
                }
            }
            break;

        case SDLK_l:
            // 重新加载瓦片
            loadBaseTiles();
            std::cout << "重新加载瓦片" << std::endl;
            break;

        case SDLK_s:
            // 显示统计信息
            if (g_appContext->tileManager)
            {
                auto stats = g_appContext->tileManager->getStatistics();
                std::cout << "=== 瓦片系统统计 ===" << std::endl;
                std::cout << "缓存大小: " << stats.cacheSize << " 字节" << std::endl;
                std::cout << "缓存数量: " << stats.cacheCount << " 个瓦片" << std::endl;
                std::cout << "下载成功: " << stats.downloadedCount << " 个瓦片" << std::endl;
                std::cout << "下载失败: " << stats.failedCount << " 个瓦片" << std::endl;
                std::cout << "总下载时间: " << stats.totalDownloadTime << " 秒" << std::endl;
            }
            break;

        case SDLK_h:
            // 显示帮助
            std::cout << "=== 控制帮助 ===" << std::endl;
            std::cout << "鼠标拖拽: 旋转地球" << std::endl;
            std::cout << "鼠标滚轮: 缩放" << std::endl;
            std::cout << "R: 重置视角" << std::endl;
            std::cout << "C: 切换坐标显示" << std::endl;
            std::cout << "T: 切换瓦片服务" << std::endl;
            std::cout << "L: 重新加载瓦片" << std::endl;
            std::cout << "S: 显示统计信息" << std::endl;
            std::cout << "H: 显示帮助" << std::endl;
            std::cout << "ESC: 退出程序" << std::endl;
            break;

        case SDLK_ESCAPE:
            std::cout << "退出程序" << std::endl;
#ifdef EMSCRIPTEN
            emscripten_cancel_main_loop();
#else
            g_appContext->shouldExit = true;
#endif
            break;
        }
    }
}

// ======== 主循环 ========

/**
 * 主循环函数
 */
void mainLoop()
{
    if (!g_appContext || !g_appContext->viewer.valid())
        return;

    // 处理SDL事件
    SDL_Event event;
    while (SDL_PollEvent(&event))
    {
        handleMouseEvent(event);
        handleKeyboardEvent(event);

        if (event.type == SDL_QUIT)
        {
#ifdef EMSCRIPTEN
            emscripten_cancel_main_loop();
#else
            g_appContext->shouldExit = true;
#endif
        }
    }

    // 渲染场景
    g_appContext->viewer->frame();

    // 交换缓冲区
    SDL_GL_SwapWindow(g_appContext->window);
}

// ======== 初始化和主函数 ========

/**
 * 初始化SDL
 */
bool initializeSDL()
{
    if (SDL_Init(SDL_INIT_VIDEO) < 0)
    {
        std::cerr << "SDL初始化失败: " << SDL_GetError() << std::endl;
        return false;
    }

    // 设置OpenGL属性
#ifdef EMSCRIPTEN
    // WebAssembly版本使用OpenGL ES
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 0);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_PROFILE_MASK, SDL_GL_CONTEXT_PROFILE_ES);
#else
    // 桌面版使用标准OpenGL
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_PROFILE_MASK, SDL_GL_CONTEXT_PROFILE_CORE);
#endif
    SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
    SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);

    // 创建窗口
    g_appContext->window = SDL_CreateWindow(
        "Enhanced OSG Earth Sample - 增强版数字地球",
        SDL_WINDOWPOS_CENTERED, SDL_WINDOWPOS_CENTERED,
        1024, 768,
        SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN | SDL_WINDOW_RESIZABLE);

    if (!g_appContext->window)
    {
        std::cerr << "窗口创建失败: " << SDL_GetError() << std::endl;
        return false;
    }

    // 创建OpenGL上下文
    g_appContext->context = SDL_GL_CreateContext(g_appContext->window);
    if (!g_appContext->context)
    {
        std::cerr << "OpenGL上下文创建失败: " << SDL_GetError() << std::endl;
        return false;
    }

    // 设置垂直同步
    SDL_GL_SetSwapInterval(1);

    std::cout << "SDL初始化成功" << std::endl;
    return true;
}

/**
 * 初始化OSG
 */
bool initializeOSG()
{
    // 创建viewer
    g_appContext->viewer = new osgViewer::Viewer();

    // 设置为嵌入式窗口
    int x, y, width, height;
    SDL_GetWindowPosition(g_appContext->window, &x, &y);
    SDL_GetWindowSize(g_appContext->window, &width, &height);

#ifdef EMSCRIPTEN
    // WebAssembly版本 - 使用简化的窗口设置
    g_appContext->viewer->setUpViewerAsEmbeddedInWindow(0, 0, width, height);

    // WebGL兼容性设置
    g_appContext->viewer->setThreadingModel(osgViewer::Viewer::SingleThreaded);

    // 禁用不支持的OpenGL功能
    osg::ref_ptr<osg::StateSet> globalStateSet = g_appContext->viewer->getCamera()->getOrCreateStateSet();

    // 禁用WebGL不支持的功能
    globalStateSet->setMode(GL_ALPHA_TEST, osg::StateAttribute::OFF);

// 下面这些常量在WebGL中不存在，需要条件编译
#ifndef __EMSCRIPTEN__
    globalStateSet->setMode(GL_POLYGON_SMOOTH, osg::StateAttribute::OFF);
    globalStateSet->setMode(GL_LINE_SMOOTH, osg::StateAttribute::OFF);
    globalStateSet->setMode(GL_POINT_SMOOTH, osg::StateAttribute::OFF);
    globalStateSet->setMode(GL_POLYGON_STIPPLE, osg::StateAttribute::OFF);
    globalStateSet->setMode(GL_LINE_STIPPLE, osg::StateAttribute::OFF);
#endif

    // 强制使用VBO
    osg::DisplaySettings::instance()->setVertexBufferHint(osg::DisplaySettings::VERTEX_BUFFER_OBJECT);

    // WebGL纹理渲染优化设置
    globalStateSet->setMode(GL_CULL_FACE, osg::StateAttribute::ON);
    globalStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);

    // 设置清除颜色为深蓝色，便于调试
    g_appContext->viewer->getCamera()->setClearColor(osg::Vec4(0.1f, 0.1f, 0.3f, 1.0f));

    std::cout << "WebGL兼容性设置完成" << std::endl;
#else
    // 桌面版本 - 使用GraphicsContext
    osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits();
    traits->x = x;
    traits->y = y;
    traits->width = width;
    traits->height = height;
    traits->windowDecoration = false;
    traits->doubleBuffer = true;
    traits->sharedContext = 0;

    osg::ref_ptr<osg::GraphicsContext> gc = osg::GraphicsContext::createGraphicsContext(traits.get());
    if (gc.valid())
    {
        g_appContext->viewer->getCamera()->setGraphicsContext(gc);
    }

    // 桌面版本使用多线程
    g_appContext->viewer->setThreadingModel(osgViewer::Viewer::SingleThreaded);
#endif

    // 创建场景
    g_appContext->rootNode = new osg::Group();

    // 创建地球节点
    osg::ref_ptr<osg::Group> earthNode = createEarthNode();

    // 创建光源
    osg::ref_ptr<osg::LightSource> lightSource = new osg::LightSource;
    osg::ref_ptr<osg::Light> light = new osg::Light;
    light->setLightNum(0);
    light->setDiffuse(osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f));  // 白色漫反射光
    light->setPosition(osg::Vec4(1.0f, 1.0f, 1.0f, 0.0f)); // 定向光
    lightSource->setLight(light);
    lightSource->setLocalStateSetModes(osg::StateAttribute::ON);

    g_appContext->rootNode->addChild(earthNode);
    g_appContext->rootNode->addChild(lightSource);

    // 设置场景
    g_appContext->viewer->setSceneData(g_appContext->rootNode);

    // 设置相机
    g_appContext->viewer->getCamera()->setViewport(new osg::Viewport(0, 0, width, height));
    g_appContext->viewer->getCamera()->setProjectionMatrixAsPerspective(30.0, (double)width / height, 1.0, 1000.0);

    // 初始化变换
    updateEarthTransform();

    std::cout << "OSG初始化成功" << std::endl;
    return true;
}

/**
 * 主函数
 */
int main(int argc, char *argv[])
{
#ifndef EMSCRIPTEN
    // 对于桌面版本，分配控制台用于调试输出
    if (AllocConsole())
    {
        freopen_s((FILE **)stdout, "CONOUT$", "w", stdout);
        freopen_s((FILE **)stderr, "CONOUT$", "w", stderr);
        freopen_s((FILE **)stdin, "CONIN$", "r", stdin);
        SetConsoleTitle("Enhanced OSG Earth Sample - Debug Output");
    }
#endif

    std::cout << "Enhanced OSG Earth Sample - 增强版数字地球示例" << std::endl;
    std::cout << "版本: 1.0.0" << std::endl;
    std::cout << "支持平台: 桌面版 + WebAssembly" << std::endl;
    std::cout << "=======================================" << std::endl;

    // 创建应用上下文
    g_appContext = new AppContext();

    // 初始化坐标系统
    g_appContext->geographicSRS = SpatialReference::createGeographic();
    g_appContext->webMercatorSRS = SpatialReference::createWebMercator();

    // 演示坐标系统功能
    demonstrateCoordinateTransform();

    // 初始化SDL
    if (!initializeSDL())
    {
        delete g_appContext;
        return -1;
    }

    // 初始化OSG
    if (!initializeOSG())
    {
        delete g_appContext;
        return -1;
    }

    // 初始化瓦片系统
    initializeTileSystem();

    // 加载基础瓦片
    loadBaseTiles();

    // 显示控制帮助
    std::cout << "\n控制说明:" << std::endl;
    std::cout << "- 鼠标拖拽: 旋转地球" << std::endl;
    std::cout << "- 鼠标滚轮: 缩放" << std::endl;
    std::cout << "- R: 重置视角" << std::endl;
    std::cout << "- C: 切换坐标显示" << std::endl;
    std::cout << "- T: 切换瓦片服务" << std::endl;
    std::cout << "- L: 重新加载瓦片" << std::endl;
    std::cout << "- S: 显示统计信息" << std::endl;
    std::cout << "- H: 显示帮助" << std::endl;
    std::cout << "- ESC: 退出程序" << std::endl;

    // 启动主循环
#ifdef EMSCRIPTEN
    emscripten_set_main_loop(mainLoop, 60, 1);
#else
    while (!g_appContext->shouldExit)
    {
        mainLoop();
        std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
    }
#endif

    // 清理资源
    cleanup();

    return 0;
}

/**
 * 清理资源
 */
void cleanup()
{
    std::cout << "开始清理资源..." << std::endl;

    if (g_appContext)
    {
        // 清理瓦片系统
        if (g_appContext->tileManager)
        {
            g_appContext->tileManager.reset();
        }

        if (g_appContext->tileDownloader)
        {
            g_appContext->tileDownloader.reset();
        }

        // 清理OSG场景
        if (g_appContext->viewer.valid())
        {
            g_appContext->viewer->setSceneData(nullptr);
            g_appContext->viewer = nullptr;
        }

        // 清理节点
        if (g_appContext->rootNode.valid())
        {
            g_appContext->rootNode->removeChildren(0, g_appContext->rootNode->getNumChildren());
            g_appContext->rootNode = nullptr;
        }

        if (g_appContext->earthTransform.valid())
        {
            g_appContext->earthTransform = nullptr;
        }

        if (g_appContext->earthGeode.valid())
        {
            g_appContext->earthGeode = nullptr;
        }

        // 清理SDL资源
        if (g_appContext->context)
        {
            SDL_GL_DeleteContext(g_appContext->context);
            g_appContext->context = nullptr;
        }

        if (g_appContext->window)
        {
            SDL_DestroyWindow(g_appContext->window);
            g_appContext->window = nullptr;
        }

        // 删除应用上下文
        delete g_appContext;
        g_appContext = nullptr;
    }

    // 清理SDL
    SDL_Quit();

    std::cout << "资源清理完成" << std::endl;
}