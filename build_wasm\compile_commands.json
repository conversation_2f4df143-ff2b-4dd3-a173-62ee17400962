[{"directory": "C:/GeminiCLI/my_osg_sample/build_wasm", "command": "C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -DEGL_EGLEXT_PROTOTYPES -DEMSCRIPTEN -DGL_GLEXT_PROTOTYPES -DOSG_GL3_AVAILABLE -DOSG_GLES2_AVAILABLE -DOSG_GLES3_AVAILABLE -IC:/GeminiCLI/my_osg_sample/1/system/include -IC:/GeminiCLI/my_osg_sample/1/system/include/SDL2 -IC:/GeminiCLI/wasm_dep/include -O3 -DNDEBUG -std=gnu++17 -o CMakeFiles\\enhanced_osg_earth_sample.dir\\main.cpp.o -c C:\\GeminiCLI\\my_osg_sample\\main.cpp", "file": "C:\\GeminiCLI\\my_osg_sample\\main.cpp", "output": "CMakeFiles\\enhanced_osg_earth_sample.dir\\main.cpp.o"}, {"directory": "C:/GeminiCLI/my_osg_sample/build_wasm", "command": "C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -DEGL_EGLEXT_PROTOTYPES -DEMSCRIPTEN -DGL_GLEXT_PROTOTYPES -DOSG_GL3_AVAILABLE -DOSG_GLES2_AVAILABLE -DOSG_GLES3_AVAILABLE -IC:/GeminiCLI/my_osg_sample/1/system/include -IC:/GeminiCLI/my_osg_sample/1/system/include/SDL2 -IC:/GeminiCLI/wasm_dep/include -O3 -DNDEBUG -std=gnu++17 -o CMakeFiles\\enhanced_osg_earth_sample.dir\\SpatialReference.cpp.o -c C:\\GeminiCLI\\my_osg_sample\\SpatialReference.cpp", "file": "C:\\GeminiCLI\\my_osg_sample\\SpatialReference.cpp", "output": "CMakeFiles\\enhanced_osg_earth_sample.dir\\SpatialReference.cpp.o"}, {"directory": "C:/GeminiCLI/my_osg_sample/build_wasm", "command": "C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -DEGL_EGLEXT_PROTOTYPES -DEMSCRIPTEN -DGL_GLEXT_PROTOTYPES -DOSG_GL3_AVAILABLE -DOSG_GLES2_AVAILABLE -DOSG_GLES3_AVAILABLE -IC:/GeminiCLI/my_osg_sample/1/system/include -IC:/GeminiCLI/my_osg_sample/1/system/include/SDL2 -IC:/GeminiCLI/wasm_dep/include -O3 -DNDEBUG -std=gnu++17 -o CMakeFiles\\enhanced_osg_earth_sample.dir\\TileSystem.cpp.o -c C:\\GeminiCLI\\my_osg_sample\\TileSystem.cpp", "file": "C:\\GeminiCLI\\my_osg_sample\\TileSystem.cpp", "output": "CMakeFiles\\enhanced_osg_earth_sample.dir\\TileSystem.cpp.o"}]