# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.26

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: enhanced_osg_earth_sample
# Configurations: Release
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Release
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/GeminiCLI/my_osg_sample/build_wasm/
# =============================================================================
# Object build statements for EXECUTABLE target enhanced_osg_earth_sample


#############################################
# Order-only phony target for enhanced_osg_earth_sample

build cmake_object_order_depends_target_enhanced_osg_earth_sample: phony || CMakeFiles/enhanced_osg_earth_sample.dir

build CMakeFiles/enhanced_osg_earth_sample.dir/main.cpp.o: CXX_COMPILER__enhanced_osg_earth_sample_unscanned_Release C$:/GeminiCLI/my_osg_sample/main.cpp || cmake_object_order_depends_target_enhanced_osg_earth_sample
  DEFINES = -DEGL_EGLEXT_PROTOTYPES -DEMSCRIPTEN -DGL_GLEXT_PROTOTYPES -DOSG_GL3_AVAILABLE -DOSG_GLES2_AVAILABLE -DOSG_GLES3_AVAILABLE
  DEP_FILE = CMakeFiles\enhanced_osg_earth_sample.dir\main.cpp.o.d
  FLAGS = -O3 -DNDEBUG -std=gnu++17
  INCLUDES = -IC:/GeminiCLI/my_osg_sample/1/system/include -IC:/GeminiCLI/my_osg_sample/1/system/include/SDL2 -IF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include
  OBJECT_DIR = CMakeFiles\enhanced_osg_earth_sample.dir
  OBJECT_FILE_DIR = CMakeFiles\enhanced_osg_earth_sample.dir

build CMakeFiles/enhanced_osg_earth_sample.dir/SpatialReference.cpp.o: CXX_COMPILER__enhanced_osg_earth_sample_unscanned_Release C$:/GeminiCLI/my_osg_sample/SpatialReference.cpp || cmake_object_order_depends_target_enhanced_osg_earth_sample
  DEFINES = -DEGL_EGLEXT_PROTOTYPES -DEMSCRIPTEN -DGL_GLEXT_PROTOTYPES -DOSG_GL3_AVAILABLE -DOSG_GLES2_AVAILABLE -DOSG_GLES3_AVAILABLE
  DEP_FILE = CMakeFiles\enhanced_osg_earth_sample.dir\SpatialReference.cpp.o.d
  FLAGS = -O3 -DNDEBUG -std=gnu++17
  INCLUDES = -IC:/GeminiCLI/my_osg_sample/1/system/include -IC:/GeminiCLI/my_osg_sample/1/system/include/SDL2 -IF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include
  OBJECT_DIR = CMakeFiles\enhanced_osg_earth_sample.dir
  OBJECT_FILE_DIR = CMakeFiles\enhanced_osg_earth_sample.dir

build CMakeFiles/enhanced_osg_earth_sample.dir/TileSystem.cpp.o: CXX_COMPILER__enhanced_osg_earth_sample_unscanned_Release C$:/GeminiCLI/my_osg_sample/TileSystem.cpp || cmake_object_order_depends_target_enhanced_osg_earth_sample
  DEFINES = -DEGL_EGLEXT_PROTOTYPES -DEMSCRIPTEN -DGL_GLEXT_PROTOTYPES -DOSG_GL3_AVAILABLE -DOSG_GLES2_AVAILABLE -DOSG_GLES3_AVAILABLE
  DEP_FILE = CMakeFiles\enhanced_osg_earth_sample.dir\TileSystem.cpp.o.d
  FLAGS = -O3 -DNDEBUG -std=gnu++17
  INCLUDES = -IC:/GeminiCLI/my_osg_sample/1/system/include -IC:/GeminiCLI/my_osg_sample/1/system/include/SDL2 -IF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include
  OBJECT_DIR = CMakeFiles\enhanced_osg_earth_sample.dir
  OBJECT_FILE_DIR = CMakeFiles\enhanced_osg_earth_sample.dir


# =============================================================================
# Link build statements for EXECUTABLE target enhanced_osg_earth_sample


#############################################
# Link the executable enhanced_osg_earth_sample.html

build enhanced_osg_earth_sample.html: CXX_EXECUTABLE_LINKER__enhanced_osg_earth_sample_Release CMakeFiles/enhanced_osg_earth_sample.dir/main.cpp.o CMakeFiles/enhanced_osg_earth_sample.dir/SpatialReference.cpp.o CMakeFiles/enhanced_osg_earth_sample.dir/TileSystem.cpp.o | F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libz.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libpng16.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libjpeg.a
  FLAGS = -O3 -DNDEBUG
  LINK_FLAGS = -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s WASM=1 -s ALLOW_MEMORY_GROWTH=1 -s MAXIMUM_MEMORY=2GB -s INITIAL_MEMORY=256MB -s STACK_SIZE=64MB -s FETCH=1 -s ASYNCIFY=1 -s ASSERTIONS=1 -s SAFE_HEAP=0 -s STACK_OVERFLOW_CHECK=1 -s NO_EXIT_RUNTIME=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_FUNCTIONS=['_main'] -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','HEAPU8'] -O3
  LINK_LIBRARIES = F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libz.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libpng16.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libjpeg.a
  OBJECT_DIR = CMakeFiles\enhanced_osg_earth_sample.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = enhanced_osg_earth_sample.html
  TARGET_PDB = enhanced_osg_earth_sample.html.dbg


#############################################
# Utility command for deploy_wasm

build deploy_wasm: phony CMakeFiles/deploy_wasm enhanced_osg_earth_sample.html


#############################################
# Utility command for quick_build_wasm

build quick_build_wasm: phony CMakeFiles/quick_build_wasm


#############################################
# Utility command for serve_wasm

build serve_wasm: phony CMakeFiles/serve_wasm deploy_wasm


#############################################
# Utility command for clean_all

build clean_all: phony CMakeFiles/clean_all


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\GeminiCLI\my_osg_sample\build_wasm && "C:\Program Files\CMake\bin\cmake-gui.exe" -SC:\GeminiCLI\my_osg_sample -BC:\GeminiCLI\my_osg_sample\build_wasm"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\GeminiCLI\my_osg_sample\build_wasm && "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -SC:\GeminiCLI\my_osg_sample -BC:\GeminiCLI\my_osg_sample\build_wasm"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cmd.exe /C "cd /D C:\GeminiCLI\my_osg_sample\build_wasm && "C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cmd.exe /C "cd /D C:\GeminiCLI\my_osg_sample\build_wasm && "C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cmd.exe /C "cd /D C:\GeminiCLI\my_osg_sample\build_wasm && "C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake"
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util


#############################################
# Custom command for CMakeFiles\deploy_wasm

build CMakeFiles/deploy_wasm | ${cmake_ninja_workdir}CMakeFiles/deploy_wasm: CUSTOM_COMMAND enhanced_osg_earth_sample.html || enhanced_osg_earth_sample.html
  COMMAND = cmd.exe /C "cd /D C:\GeminiCLI\my_osg_sample\build_wasm && "C:\Program Files\CMake\bin\cmake.exe" -E make_directory C:/GeminiCLI/my_osg_sample/redist_wasm/bin && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/GeminiCLI/my_osg_sample/build_wasm/enhanced_osg_earth_sample.html C:/GeminiCLI/my_osg_sample/build_wasm/enhanced_osg_earth_sample.js C:/GeminiCLI/my_osg_sample/build_wasm/enhanced_osg_earth_sample.wasm C:/GeminiCLI/my_osg_sample/redist_wasm/bin/"
  DESC = Deploying WebAssembly files to redist_wasm


#############################################
# Custom command for CMakeFiles\quick_build_wasm

build CMakeFiles/quick_build_wasm | ${cmake_ninja_workdir}CMakeFiles/quick_build_wasm: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\GeminiCLI\my_osg_sample\build_wasm && "C:\Program Files\CMake\bin\cmake.exe" --build C:/GeminiCLI/my_osg_sample/build_wasm --target enhanced_osg_earth_sample && "C:\Program Files\CMake\bin\cmake.exe" --build C:/GeminiCLI/my_osg_sample/build_wasm --target deploy_wasm"
  DESC = Quick build and deploy WebAssembly version


#############################################
# Custom command for CMakeFiles\serve_wasm

build CMakeFiles/serve_wasm | ${cmake_ninja_workdir}CMakeFiles/serve_wasm: CUSTOM_COMMAND || deploy_wasm enhanced_osg_earth_sample.html
  COMMAND = cmd.exe /C "cd /D C:\GeminiCLI\my_osg_sample\build_wasm && python -m http.server 8000 -d C:/GeminiCLI/my_osg_sample/../../../redist_wasm"
  DESC = Start HTTP server for WebAssembly testing


#############################################
# Custom command for CMakeFiles\clean_all

build CMakeFiles/clean_all | ${cmake_ninja_workdir}CMakeFiles/clean_all: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\GeminiCLI\my_osg_sample\build_wasm && "C:\Program Files\CMake\bin\cmake.exe" -E remove_directory C:/GeminiCLI/my_osg_sample/build_wasm && "C:\Program Files\CMake\bin\cmake.exe" -E remove_directory C:/GeminiCLI/my_osg_sample/build_wasm"
  DESC = Clean all build artifacts

# =============================================================================
# Target aliases.

build enhanced_osg_earth_sample: phony enhanced_osg_earth_sample.html

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/GeminiCLI/my_osg_sample/build_wasm

build all: phony enhanced_osg_earth_sample.html deploy_wasm

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/GeminiCLI/my_osg_sample/CMakeLists.txt C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-C.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-CXX.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/GNU.cmake C$:/dev/emsdk/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake CMakeCache.txt CMakeFiles/3.26.4/CMakeCCompiler.cmake CMakeFiles/3.26.4/CMakeCXXCompiler.cmake CMakeFiles/3.26.4/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/GeminiCLI/my_osg_sample/CMakeLists.txt C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-C.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-CXX.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/GNU.cmake C$:/dev/emsdk/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake CMakeCache.txt CMakeFiles/3.26.4/CMakeCCompiler.cmake CMakeFiles/3.26.4/CMakeCXXCompiler.cmake CMakeFiles/3.26.4/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
