﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{AE587409-90FA-3582-ABF9-5F95A7893E2A}"
	ProjectSection(ProjectDependencies) = postProject
		{6EC7BB37-D250-3A7E-BD42-10D595FAABC1} = {6EC7BB37-D250-3A7E-BD42-10D595FAABC1}
		{300A4CD5-5D39-31A6-9950-3DEBA55FB9E7} = {300A4CD5-5D39-31A6-9950-3DEBA55FB9E7}
		{849E9659-DB8F-3CED-9A42-D983BEE1390C} = {849E9659-DB8F-3CED-9A42-D983<PERSON><PERSON>1390C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{E49853B0-8FA9-3F35-AAB7-FA6569D32BE5}"
	ProjectSection(ProjectDependencies) = postProject
		{AE587409-90FA-3582-ABF9-5F95A7893E2A} = {AE587409-90FA-3582-ABF9-5F95A7893E2A}
		{6EC7BB37-D250-3A7E-BD42-10D595FAABC1} = {6EC7BB37-D250-3A7E-BD42-10D595FAABC1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{6EC7BB37-D250-3A7E-BD42-10D595FAABC1}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "clean_all", "clean_all.vcxproj", "{8D52167F-8E58-312F-BCA7-1888360077C0}"
	ProjectSection(ProjectDependencies) = postProject
		{6EC7BB37-D250-3A7E-BD42-10D595FAABC1} = {6EC7BB37-D250-3A7E-BD42-10D595FAABC1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "deploy_desk", "deploy_desk.vcxproj", "{300A4CD5-5D39-31A6-9950-3DEBA55FB9E7}"
	ProjectSection(ProjectDependencies) = postProject
		{6EC7BB37-D250-3A7E-BD42-10D595FAABC1} = {6EC7BB37-D250-3A7E-BD42-10D595FAABC1}
		{849E9659-DB8F-3CED-9A42-D983BEE1390C} = {849E9659-DB8F-3CED-9A42-D983BEE1390C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "enhanced_osg_earth_sample", "enhanced_osg_earth_sample.vcxproj", "{849E9659-DB8F-3CED-9A42-D983BEE1390C}"
	ProjectSection(ProjectDependencies) = postProject
		{6EC7BB37-D250-3A7E-BD42-10D595FAABC1} = {6EC7BB37-D250-3A7E-BD42-10D595FAABC1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "quick_build_desk", "quick_build_desk.vcxproj", "{26B26366-A4F6-3D7F-93B5-ED0505A1B667}"
	ProjectSection(ProjectDependencies) = postProject
		{6EC7BB37-D250-3A7E-BD42-10D595FAABC1} = {6EC7BB37-D250-3A7E-BD42-10D595FAABC1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "run_desk", "run_desk.vcxproj", "{D557E8BE-DCEF-3D20-9319-9D96BC39F6DE}"
	ProjectSection(ProjectDependencies) = postProject
		{6EC7BB37-D250-3A7E-BD42-10D595FAABC1} = {6EC7BB37-D250-3A7E-BD42-10D595FAABC1}
		{300A4CD5-5D39-31A6-9950-3DEBA55FB9E7} = {300A4CD5-5D39-31A6-9950-3DEBA55FB9E7}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{AE587409-90FA-3582-ABF9-5F95A7893E2A}.Debug|x64.ActiveCfg = Debug|x64
		{AE587409-90FA-3582-ABF9-5F95A7893E2A}.Debug|x64.Build.0 = Debug|x64
		{AE587409-90FA-3582-ABF9-5F95A7893E2A}.Release|x64.ActiveCfg = Release|x64
		{AE587409-90FA-3582-ABF9-5F95A7893E2A}.Release|x64.Build.0 = Release|x64
		{AE587409-90FA-3582-ABF9-5F95A7893E2A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{AE587409-90FA-3582-ABF9-5F95A7893E2A}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{AE587409-90FA-3582-ABF9-5F95A7893E2A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{AE587409-90FA-3582-ABF9-5F95A7893E2A}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{E49853B0-8FA9-3F35-AAB7-FA6569D32BE5}.Debug|x64.ActiveCfg = Debug|x64
		{E49853B0-8FA9-3F35-AAB7-FA6569D32BE5}.Release|x64.ActiveCfg = Release|x64
		{E49853B0-8FA9-3F35-AAB7-FA6569D32BE5}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{E49853B0-8FA9-3F35-AAB7-FA6569D32BE5}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{6EC7BB37-D250-3A7E-BD42-10D595FAABC1}.Debug|x64.ActiveCfg = Debug|x64
		{6EC7BB37-D250-3A7E-BD42-10D595FAABC1}.Debug|x64.Build.0 = Debug|x64
		{6EC7BB37-D250-3A7E-BD42-10D595FAABC1}.Release|x64.ActiveCfg = Release|x64
		{6EC7BB37-D250-3A7E-BD42-10D595FAABC1}.Release|x64.Build.0 = Release|x64
		{6EC7BB37-D250-3A7E-BD42-10D595FAABC1}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{6EC7BB37-D250-3A7E-BD42-10D595FAABC1}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{6EC7BB37-D250-3A7E-BD42-10D595FAABC1}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{6EC7BB37-D250-3A7E-BD42-10D595FAABC1}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{8D52167F-8E58-312F-BCA7-1888360077C0}.Debug|x64.ActiveCfg = Debug|x64
		{8D52167F-8E58-312F-BCA7-1888360077C0}.Release|x64.ActiveCfg = Release|x64
		{8D52167F-8E58-312F-BCA7-1888360077C0}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8D52167F-8E58-312F-BCA7-1888360077C0}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{300A4CD5-5D39-31A6-9950-3DEBA55FB9E7}.Debug|x64.ActiveCfg = Debug|x64
		{300A4CD5-5D39-31A6-9950-3DEBA55FB9E7}.Debug|x64.Build.0 = Debug|x64
		{300A4CD5-5D39-31A6-9950-3DEBA55FB9E7}.Release|x64.ActiveCfg = Release|x64
		{300A4CD5-5D39-31A6-9950-3DEBA55FB9E7}.Release|x64.Build.0 = Release|x64
		{300A4CD5-5D39-31A6-9950-3DEBA55FB9E7}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{300A4CD5-5D39-31A6-9950-3DEBA55FB9E7}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{300A4CD5-5D39-31A6-9950-3DEBA55FB9E7}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{300A4CD5-5D39-31A6-9950-3DEBA55FB9E7}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{849E9659-DB8F-3CED-9A42-D983BEE1390C}.Debug|x64.ActiveCfg = Debug|x64
		{849E9659-DB8F-3CED-9A42-D983BEE1390C}.Debug|x64.Build.0 = Debug|x64
		{849E9659-DB8F-3CED-9A42-D983BEE1390C}.Release|x64.ActiveCfg = Release|x64
		{849E9659-DB8F-3CED-9A42-D983BEE1390C}.Release|x64.Build.0 = Release|x64
		{849E9659-DB8F-3CED-9A42-D983BEE1390C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{849E9659-DB8F-3CED-9A42-D983BEE1390C}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{849E9659-DB8F-3CED-9A42-D983BEE1390C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{849E9659-DB8F-3CED-9A42-D983BEE1390C}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{26B26366-A4F6-3D7F-93B5-ED0505A1B667}.Debug|x64.ActiveCfg = Debug|x64
		{26B26366-A4F6-3D7F-93B5-ED0505A1B667}.Release|x64.ActiveCfg = Release|x64
		{26B26366-A4F6-3D7F-93B5-ED0505A1B667}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{26B26366-A4F6-3D7F-93B5-ED0505A1B667}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{D557E8BE-DCEF-3D20-9319-9D96BC39F6DE}.Debug|x64.ActiveCfg = Debug|x64
		{D557E8BE-DCEF-3D20-9319-9D96BC39F6DE}.Release|x64.ActiveCfg = Release|x64
		{D557E8BE-DCEF-3D20-9319-9D96BC39F6DE}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{D557E8BE-DCEF-3D20-9319-9D96BC39F6DE}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {74EF2351-A285-35C5-AEE5-5DF9955F6CB6}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
