cmake_minimum_required(VERSION 3.10)
project(enhanced_osg_earth_sample)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
if(EMSCRIPTEN)
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/build_wasm)
    set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/build_wasm)
else()
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/build_desk)
    set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/build_desk)
endif()

# 源文件
set(SOURCES
    main.cpp
    SpatialReference.cpp
    TileSystem.cpp
)

# 头文件
set(HEADERS
    SpatialReference.hpp
    TileSystem.hpp
)

# 添加可执行文件
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# 编译器和链接器选项
if(EMSCRIPTEN)
    # ========== WebAssembly 构建配置 ==========
    
    # 编译器标志
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        EMSCRIPTEN
        GL_GLEXT_PROTOTYPES
        EGL_EGLEXT_PROTOTYPES
    )
    
    # WebAssembly链接器标志
    set(EMSCRIPTEN_LINK_FLAGS
        "-s USE_SDL=2"
        "-s USE_WEBGL2=1"
        "-s FULL_ES3=1"
        "-s WASM=1"
        "-s ALLOW_MEMORY_GROWTH=1"
        "-s MAXIMUM_MEMORY=2GB"
        "-s INITIAL_MEMORY=256MB"
        "-s STACK_SIZE=64MB"
        "-s FETCH=1"
        "-s ASYNCIFY=1"
        "-s ASSERTIONS=1"
        "-s SAFE_HEAP=0"
        "-s STACK_OVERFLOW_CHECK=1"
        "-s NO_EXIT_RUNTIME=1"
        "-s DISABLE_EXCEPTION_CATCHING=0"
        "-s EXPORTED_FUNCTIONS=['_main']"
        "-s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','HEAPU8']"
        "-O3"
        ""
    )
    
    # 应用链接器标志
    string(REPLACE ";" " " EMSCRIPTEN_LINK_FLAGS_STR "${EMSCRIPTEN_LINK_FLAGS}")
    set_target_properties(${PROJECT_NAME} PROPERTIES
        SUFFIX ".html"
        LINK_FLAGS "${EMSCRIPTEN_LINK_FLAGS_STR}"
    )
    
    # WebAssembly特定的包含目录
    set(OSG_WASM_LIB_DIR "F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep")
    target_include_directories(${PROJECT_NAME} PRIVATE
        ${EMSCRIPTEN}/system/include
        ${EMSCRIPTEN}/system/include/SDL2
        ${OSG_WASM_LIB_DIR}/include
    )
    
    # WebAssembly OSG库链接
    set(OSG_LIBRARIES
        "${OSG_WASM_LIB_DIR}/lib/libosg.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgViewer.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgDB.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgGA.a"
        "${OSG_WASM_LIB_DIR}/lib/libosgUtil.a"
        "${OSG_WASM_LIB_DIR}/lib/libOpenThreads.a"
    )
    
    # 第三方库
    set(THIRD_PARTY_LIBRARIES
        "${OSG_WASM_LIB_DIR}/lib/libz.a"
        "${OSG_WASM_LIB_DIR}/lib/libpng16.a"
        "${OSG_WASM_LIB_DIR}/lib/libjpeg.a"
    )
    
    # 链接库
    target_link_libraries(${PROJECT_NAME} ${OSG_LIBRARIES} ${THIRD_PARTY_LIBRARIES})
    
    message(STATUS "WebAssembly build configured")
    message(STATUS "Output files will be generated in: ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}")
    
else()
    # ========== 桌面版构建配置 ==========
    
    # 设置固定的VCPKG路径
    set(VCPKG_ROOT "C:/dev/vcpkg")
    set(VCPKG_INSTALLED_DIR "C:/dev/vcpkg/installed/x64-windows")
    
    # Windows系统优先使用VCPKG，Unix系统使用pkg-config
    if(WIN32)
        # Windows: 使用VCPKG查找SDL2
        find_package(SDL2 CONFIG QUIET)
        if(NOT SDL2_FOUND)
            # 使用固定的VCPKG路径
            find_library(SDL2_LIBRARIES NAMES SDL2 PATHS "${VCPKG_INSTALLED_DIR}/lib" NO_DEFAULT_PATH)
            find_path(SDL2_INCLUDE_DIRS NAMES SDL.h PATHS "${VCPKG_INSTALLED_DIR}/include/SDL2" NO_DEFAULT_PATH)
            
            if(SDL2_LIBRARIES AND SDL2_INCLUDE_DIRS)
                set(SDL2_FOUND TRUE)
                message(STATUS "Found SDL2 via VCPKG: ${SDL2_LIBRARIES}")
            endif()
        endif()
        
        if(SDL2_FOUND)
            # 简单的库文件链接方式
            target_link_libraries(${PROJECT_NAME} ${SDL2_LIBRARIES})
            target_include_directories(${PROJECT_NAME} PRIVATE ${SDL2_INCLUDE_DIRS})
            # 告诉SDL2不要替换main函数
            target_compile_definitions(${PROJECT_NAME} PRIVATE SDL_MAIN_HANDLED)
            # 添加SDL2main库
            find_library(SDL2_MAIN_LIBRARY NAMES SDL2main PATHS "${VCPKG_INSTALLED_DIR}/lib" NO_DEFAULT_PATH)
            if(SDL2_MAIN_LIBRARY)
                target_link_libraries(${PROJECT_NAME} ${SDL2_MAIN_LIBRARY})
            endif()
        else()
            message(WARNING "SDL2 not found, will try to continue without it")
        endif()
    else()
        # Unix: 使用pkg-config查找SDL2
        find_package(PkgConfig REQUIRED)
        pkg_check_modules(SDL2 REQUIRED sdl2)
        target_link_libraries(${PROJECT_NAME} ${SDL2_LIBRARIES})
        target_include_directories(${PROJECT_NAME} PRIVATE ${SDL2_INCLUDE_DIRS})
        target_compile_options(${PROJECT_NAME} PRIVATE ${SDL2_CFLAGS_OTHER})
    endif()
    
    # OpenSceneGraph - 使用VCPKG提供的现代CMake配置
    find_package(osg CONFIG QUIET)
    if(osg_FOUND)
        message(STATUS "Found OSG via VCPKG config")
        target_link_libraries(${PROJECT_NAME} 
            osg::osg 
            osg::osgViewer 
            osg::osgDB 
            osg::osgGA 
            osg::osgUtil
            osg::OpenThreads
        )
    else()
        # 回退到传统的FindOpenSceneGraph
        find_package(OpenSceneGraph QUIET COMPONENTS osg osgViewer osgDB osgGA osgUtil)
        if(OPENSCENEGRAPH_FOUND)
            target_link_libraries(${PROJECT_NAME} ${OPENSCENEGRAPH_LIBRARIES})
            target_include_directories(${PROJECT_NAME} PRIVATE ${OPENSCENEGRAPH_INCLUDE_DIRS})
        else()
            # 手动指定固定的VCPKG路径
            set(OSG_INCLUDE_DIR "${VCPKG_INSTALLED_DIR}/include")
            set(OSG_LIBRARY_DIR "${VCPKG_INSTALLED_DIR}/lib")
            
            message(STATUS "Using manual OSG paths: ${OSG_INCLUDE_DIR}")
            target_include_directories(${PROJECT_NAME} PRIVATE ${OSG_INCLUDE_DIR})
            target_link_directories(${PROJECT_NAME} PRIVATE ${OSG_LIBRARY_DIR})
            
            target_link_libraries(${PROJECT_NAME}
                osg osgViewer osgDB osgGA osgUtil OpenThreads
            )
        endif()
    endif()
    
    # OpenGL
    find_package(OpenGL REQUIRED)
    target_link_libraries(${PROJECT_NAME} ${OPENGL_LIBRARIES})
    
    # 其他系统库
    if(WIN32)
        target_link_libraries(${PROJECT_NAME} ws2_32 winmm)
        
        # Microsoft Visual C++编译器UTF-8支持
        if(MSVC)
            target_compile_options(${PROJECT_NAME} PRIVATE /utf-8)
            # 设置运行时库
            set_property(TARGET ${PROJECT_NAME} PROPERTY MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")
            # 设置控制台子系统（保持控制台输出）
            set_property(TARGET ${PROJECT_NAME} PROPERTY LINK_FLAGS "/SUBSYSTEM:CONSOLE")
        endif()
        
    elseif(UNIX)
        target_link_libraries(${PROJECT_NAME} pthread dl)
        
        # Linux特定的库查找
        find_package(Threads REQUIRED)
        target_link_libraries(${PROJECT_NAME} Threads::Threads)
    endif()
    
    # 编译器特定设置
    if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
        target_compile_options(${PROJECT_NAME} PRIVATE
            -Wall -Wextra -Wno-unused-parameter
            -O3 -DNDEBUG
        )
    endif()
    
    message(STATUS "Desktop build configured")
    message(STATUS "Output directory: ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}")
endif()

# 通用编译定义
target_compile_definitions(${PROJECT_NAME} PRIVATE
    OSG_GL3_AVAILABLE
    OSG_GLES2_AVAILABLE
    OSG_GLES3_AVAILABLE
)

# 调试信息
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(${PROJECT_NAME} PRIVATE DEBUG_BUILD)
    if(NOT EMSCRIPTEN)
        target_compile_options(${PROJECT_NAME} PRIVATE -g)
    endif()
endif()

# 安装规则
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

if(EMSCRIPTEN)
    # WebAssembly文件安装
    install(FILES 
        "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${PROJECT_NAME}.html"
        "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${PROJECT_NAME}.js"
        "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${PROJECT_NAME}.wasm"
        "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${PROJECT_NAME}.data"
        DESTINATION bin
        OPTIONAL
    )
    
    # 复制到redist_wasm目录
    add_custom_target(deploy_wasm ALL
        COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_CURRENT_SOURCE_DIR}/redist_wasm/bin"
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
            "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${PROJECT_NAME}.html"
            "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${PROJECT_NAME}.js"
            "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${PROJECT_NAME}.wasm"
            "${CMAKE_CURRENT_SOURCE_DIR}/redist_wasm/bin/"
        DEPENDS ${PROJECT_NAME}
        COMMENT "Deploying WebAssembly files to redist_wasm"
    )
    
else()
    # 桌面版部署
    add_custom_target(deploy_desk ALL
        COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_CURRENT_SOURCE_DIR}/../../../redist_desk/bin"
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
            "$<TARGET_FILE:${PROJECT_NAME}>"
            "${CMAKE_CURRENT_SOURCE_DIR}/../../../redist_desk/bin/"
        DEPENDS ${PROJECT_NAME}
        COMMENT "Deploying desktop application to redist_desk"
    )
endif()

# 显示构建信息
message(STATUS "====================================")
message(STATUS "Enhanced OSG Earth Sample")
message(STATUS "====================================")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
if(EMSCRIPTEN)
    message(STATUS "Platform: WebAssembly (Emscripten)")
    message(STATUS "Emscripten: ${EMSCRIPTEN}")
    message(STATUS "Output: ${PROJECT_NAME}.html, ${PROJECT_NAME}.js, ${PROJECT_NAME}.wasm")
else()
    message(STATUS "Platform: Desktop (${CMAKE_SYSTEM_NAME})")
    message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID}")
    message(STATUS "Output: ${PROJECT_NAME}${CMAKE_EXECUTABLE_SUFFIX}")
endif()
message(STATUS "Source directory: ${CMAKE_CURRENT_SOURCE_DIR}")
message(STATUS "Binary directory: ${CMAKE_CURRENT_BINARY_DIR}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "====================================")

# 添加自定义目标用于快速构建和测试
if(EMSCRIPTEN)
    add_custom_target(quick_build_wasm
        COMMAND ${CMAKE_COMMAND} --build ${CMAKE_BINARY_DIR} --target ${PROJECT_NAME}
        COMMAND ${CMAKE_COMMAND} --build ${CMAKE_BINARY_DIR} --target deploy_wasm
        COMMENT "Quick build and deploy WebAssembly version"
    )
    
    add_custom_target(serve_wasm
        COMMAND python -m http.server 8000 -d "${CMAKE_CURRENT_SOURCE_DIR}/../../../redist_wasm"
        COMMENT "Start HTTP server for WebAssembly testing"
        DEPENDS deploy_wasm
    )
else()
    add_custom_target(quick_build_desk
        COMMAND ${CMAKE_COMMAND} --build ${CMAKE_BINARY_DIR} --target ${PROJECT_NAME}
        COMMAND ${CMAKE_COMMAND} --build ${CMAKE_BINARY_DIR} --target deploy_desk
        COMMENT "Quick build and deploy desktop version"
    )
    
    add_custom_target(run_desk
        COMMAND "${CMAKE_CURRENT_SOURCE_DIR}/../../../redist_desk/bin/${PROJECT_NAME}${CMAKE_EXECUTABLE_SUFFIX}"
        COMMENT "Run desktop application"
        DEPENDS deploy_desk
    )
endif()

# 清理目标
add_custom_target(clean_all
    COMMAND ${CMAKE_COMMAND} -E remove_directory "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}"
    COMMAND ${CMAKE_COMMAND} -E remove_directory "${CMAKE_CURRENT_BINARY_DIR}"
    COMMENT "Clean all build artifacts"
) 